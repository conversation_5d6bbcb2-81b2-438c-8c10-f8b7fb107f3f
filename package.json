{"name": "cantv-ding", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "build:check": "tsc --noEmit", "build:force": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit<PERSON>n<PERSON><PERSON>r false", "start": "NODE_ENV=production node dist/index.js", "test": "node scripts/test-dingtalk.js", "test:api": "node scripts/test-api-endpoints.js", "test:project": "node scripts/test-project-api.js", "test:db": "node scripts/test-database.js", "test:all": "node scripts/test-all-apis.js", "test:docs": "node scripts/validate-api-docs.js", "test:timezone": "tsx src/scripts/testTimezone.ts", "diagnose": "node scripts/diagnose-jsapi.js", "quick-start": "node scripts/quick-start.js", "db:setup": "node scripts/setup-database.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio", "db:seed": "node scripts/migrate-to-postgres.js", "db:reset": "prisma migrate reset", "permissions:check": "pnpm tsx src/scripts/checkPermissionStatus.ts status", "permissions:report": "pnpm tsx src/scripts/checkPermissionStatus.ts report", "permissions:role": "pnpm tsx src/scripts/checkPermissionStatus.ts role", "permissions:init": "pnpm tsx src/scripts/initializePermissions.ts", "permissions:reinit": "pnpm tsx src/scripts/initializePermissions.ts reinit", "test:user-sync": "pnpm tsx src/scripts/testUserSync.ts"}, "keywords": ["<PERSON><PERSON><PERSON>", "h5", "api", "fastify", "typescript"], "author": "<EMAIL>", "license": "ISC", "description": "钉钉微H5应用后端API服务", "packageManager": "pnpm@9.1.0+sha512.67f5879916a9293e5cf059c23853d571beaf4f753c707f40cb22bed5fb1578c6aad3b6c4107ccb3ba0b35be003eb621a16471ac836c87beb53f9d54bb4612724", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/env": "^5.0.2", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.2.0", "@prisma/client": "^6.9.0", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.9", "@types/node-cron": "^3.0.11", "@types/redis": "^4.0.10", "@types/ws": "^8.18.1", "axios": "^1.9.0", "crypto-js": "^4.2.0", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "fastify": "^5.3.3", "jsonwebtoken": "^9.0.2", "node-cron": "^4.1.0", "pino": "^9.7.0", "prisma": "^6.9.0", "redis": "^5.5.6", "ws": "^8.18.2", "zod": "^3.25.56"}, "devDependencies": {"@types/node": "^22.15.30", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "pino-pretty": "^13.0.0", "tsc-alias": "^1.8.16", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-transform-paths": "^3.5.5"}}